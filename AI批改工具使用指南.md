# AI自动化批改工具使用指南

## 工具概述
本指南提供了一套完整的AI自动化批改工具，用于评估《Web前端框架应用》课程设计项目。AI可以使用这些工具来自动分析学生作品并生成评分报告。

## 核心分析工具

### 1. 项目结构分析工具
**功能**: 检查项目文件组织结构
**使用方法**: 
```
使用 view 工具查看项目根目录，检查以下文件：
- index.html (主页面)
- css/ 目录 (样式文件)
- js/ 目录 (脚本文件)  
- img/ 目录 (图片资源)
- README.md (项目说明)
```

**评分标准**:
- 文件结构完整 (5分)
- 命名规范 (3分)
- 目录组织合理 (2分)

### 2. HTML代码质量分析工具
**功能**: 分析HTML代码的语义化和结构合理性
**使用方法**:
```
使用 view 工具查看 index.html 和其他HTML文件，检查：
- DOCTYPE声明
- meta标签设置
- HTML5语义化标签使用
- Bootstrap类名使用
- 表单结构
- 导航结构
```

**关键检查点**:
- `<!DOCTYPE html>` 声明
- `<meta charset="UTF-8">` 字符编码
- `<meta name="viewport">` 响应式设置
- Bootstrap CSS/JS正确引入
- 语义化标签: `<nav>`, `<main>`, `<section>`, `<article>`
- Bootstrap组件: `.navbar`, `.modal`, `.card`, `.form-group`

### 3. CSS样式分析工具
**功能**: 检查CSS样式的规范性和Bootstrap使用情况
**使用方法**:
```
使用 view 工具查看CSS文件，分析：
- Bootstrap框架引入
- 自定义样式编写
- 响应式设计实现
- 选择器使用规范
```

**评分要点**:
- Bootstrap正确引入 (5分)
- 自定义样式合理 (3分)
- 响应式类使用 (2分)

### 4. JavaScript功能分析工具
**功能**: 检查JavaScript交互功能实现
**使用方法**:
```
使用 view 工具查看JS文件，检查：
- jQuery库引入
- Bootstrap JS组件使用
- 自定义交互功能
- 事件处理
```

### 5. Bootstrap组件使用分析工具
**功能**: 专门检查Bootstrap框架组件的使用情况
**检查清单**:
- 栅格系统: `.container`, `.row`, `.col-*`
- 导航组件: `.navbar`, `.nav`, `.navbar-nav`
- 按钮组件: `.btn`, `.btn-primary`, `.btn-secondary`
- 表单组件: `.form-group`, `.form-control`, `.form-check`
- 模态框: `.modal`, `.modal-dialog`, `.modal-content`
- 卡片组件: `.card`, `.card-body`, `.card-header`

## 自动化评分流程

### 第一步: 项目概览
```
1. 使用 view 工具查看项目根目录
2. 检查文件结构完整性
3. 查看 README.md 了解项目说明
4. 记录项目基本信息
```

### 第二步: 主页面分析
```
1. 使用 view 工具查看 index.html
2. 检查HTML结构和Bootstrap使用
3. 分析页面功能模块
4. 评估代码质量
```

### 第三步: 样式文件分析
```
1. 查看 css/ 目录下的文件
2. 检查Bootstrap引入情况
3. 分析自定义样式
4. 评估响应式设计
```

### 第四步: 脚本文件分析
```
1. 查看 js/ 目录下的文件
2. 检查jQuery和Bootstrap JS引入
3. 分析交互功能实现
4. 评估代码规范性
```

### 第五步: 功能完整性检查
```
根据项目要求检查以下功能模块：
- 导航栏设计
- 首页搜索模块
- 房源展示模块
- 用户登录注册
- 预订功能
- 个人中心
```

## 评分计算公式

### 技术实现 (40分)
```
Bootstrap应用分数 = (正确使用的组件数 / 要求组件总数) × 15
HTML结构分数 = (语义化程度 + 结构合理性) × 5
CSS样式分数 = (样式规范性 + 自定义样式质量) × 5  
JavaScript分数 = (功能实现度 + 代码质量) × 2.5
```

### 功能完整性 (30分)
```
各功能模块分数 = (实现功能数 / 要求功能数) × 对应分值
总功能分数 = 各模块分数之和
```

### 界面设计 (20分)
```
视觉效果分数 = (色彩搭配 + 布局美观 + 视觉层次) × 3.33
用户体验分数 = (交互流畅度 + 易用性 + 响应式效果) × 3.33
```

### 代码质量 (10分)
```
代码规范分数 = (命名规范 + 缩进统一 + 注释完整) × 1.67
可维护性分数 = (结构清晰 + 模块化 + 复用性) × 1.67
```

## 批改报告生成模板

### 使用 render-mermaid 工具生成评分图表
```mermaid
pie title 评分分布
    "技术实现" : 40
    "功能完整性" : 30
    "界面设计" : 20
    "代码质量" : 10
```

### 详细评分报告格式
```
=== Web前端框架应用课程设计评分报告 ===

学生信息:
- 姓名: [从项目信息中获取]
- 项目: 民宿预订系统
- 提交时间: [当前时间]

总体评分: [X]/100分
评级: [优秀(90-100)/良好(80-89)/及格(60-79)/不及格(0-59)]

详细评分:
┌─────────────────────────────────────┐
│ 1. 技术实现 (40分)                    │
├─────────────────────────────────────┤
│ Bootstrap应用: [X]/15分               │
│ HTML结构: [X]/10分                   │
│ CSS样式: [X]/10分                    │
│ JavaScript交互: [X]/5分              │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ 2. 功能完整性 (30分)                  │
├─────────────────────────────────────┤
│ 导航栏设计: [X]/5分                  │
│ 首页布局: [X]/8分                    │
│ 房源详情: [X]/8分                    │
│ 用户中心: [X]/5分                    │
│ 登录注册: [X]/4分                    │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ 3. 界面设计 (20分)                    │
├─────────────────────────────────────┤
│ 视觉效果: [X]/10分                   │
│ 用户体验: [X]/10分                   │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ 4. 代码质量 (10分)                    │
├─────────────────────────────────────┤
│ 代码规范: [X]/5分                    │
│ 可维护性: [X]/5分                    │
└─────────────────────────────────────┘

项目亮点:
✓ [具体优点1]
✓ [具体优点2]
✓ [具体优点3]

改进建议:
⚠ [具体问题1及解决方案]
⚠ [具体问题2及解决方案]
⚠ [具体问题3及解决方案]

技术建议:
💡 [技术改进建议1]
💡 [技术改进建议2]
💡 [技术改进建议3]

总结:
[对学生作品的总体评价和鼓励]
```

## 使用示例

当需要批改学生作品时，AI应该按以下步骤操作：

1. **项目分析**: 使用 `view` 工具查看项目结构
2. **代码检查**: 逐个查看HTML、CSS、JS文件
3. **功能评估**: 根据要求检查功能实现情况
4. **评分计算**: 按照评分标准计算各项分数
5. **报告生成**: 使用模板生成详细评分报告
6. **图表展示**: 使用 `render-mermaid` 生成可视化评分图表

这套工具确保了评分的客观性、一致性和全面性，同时为学生提供了具体的改进指导。
