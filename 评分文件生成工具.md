# 评分文件生成工具

## 功能说明
此工具用于将AI批改结果转换为标准的评分文件格式，便于教务管理和成绩录入。

## CSV评分文件格式

### 标准表头
```csv
姓名,班级,序号,设计主题,代码规范,模块化,错误处理,文档质量,布局工具类,网格系统,响应式布局,字体图标,表单组件,下拉菜单,卡片组件,轮播组件,模态框组件,导航组件,导航栏组件,媒体对象组件,弹出框组件,版本控制,设计主题评分,用户体验,总分,评级
```

### 字段说明
- **姓名**: 学生姓名
- **班级**: 学生班级
- **序号**: 学号或序号
- **设计主题**: 项目主题名称
- **代码规范**: 第1项评分 (0-5分)
- **模块化**: 第2项评分 (0-5分)
- **错误处理**: 第3项评分 (0-5分)
- **文档质量**: 第4项评分 (0-5分)
- **布局工具类**: 第5项评分 (0-5分)
- **网格系统**: 第6项评分 (0-5分)
- **响应式布局**: 第7项评分 (0-5分)
- **字体图标**: 第8项评分 (0-5分)
- **表单组件**: 第9项评分 (0-5分)
- **下拉菜单**: 第10项评分 (0-5分)
- **卡片组件**: 第11项评分 (0-5分)
- **轮播组件**: 第12项评分 (0-5分)
- **模态框组件**: 第13项评分 (0-5分)
- **导航组件**: 第14项评分 (0-5分)
- **导航栏组件**: 第15项评分 (0-5分)
- **媒体对象组件**: 第16项评分 (0-5分)
- **弹出框组件**: 第17项评分 (0-5分)
- **版本控制**: 第18项评分 (0-5分)
- **设计主题评分**: 第19项评分 (0-5分)
- **用户体验**: 第20项评分 (0-5分)
- **总分**: 总分 (0-100分)
- **评级**: 优秀/良好/中等/及格/不及格

## Excel评分文件格式

### 工作表结构
```
Sheet1: 评分汇总
- 包含所有学生的评分数据
- 自动计算总分和评级
- 包含统计信息

Sheet2: 评分明细
- 详细的评分说明
- 各项评分标准
- 扣分原因说明
```

### Excel模板示例
```
| 姓名 | 班级 | 序号 | 设计主题 | 第1项 | 第2项 | ... | 第20项 | 总分 | 评级 |
|------|------|------|----------|-------|-------|-----|--------|------|------|
| 张三 | 1班  | 001  | 电商网站 |   4   |   3   | ... |   5    |  78  | 良好 |
| 李四 | 1班  | 002  | 博客系统 |   3   |   4   | ... |   4    |  82  | 良好 |
```

## 自动化生成脚本

### Python脚本示例
```python
import csv
import pandas as pd
from datetime import datetime

def generate_score_file(student_scores, output_format='csv'):
    """
    生成评分文件
    
    Args:
        student_scores: 学生评分数据列表
        output_format: 输出格式 ('csv' 或 'excel')
    """
    
    # 定义表头
    headers = [
        '姓名', '班级', '序号', '设计主题',
        '代码规范', '模块化', '错误处理', '文档质量',
        '布局工具类', '网格系统', '响应式布局', '字体图标',
        '表单组件', '下拉菜单', '卡片组件', '轮播组件',
        '模态框组件', '导航组件', '导航栏组件', '媒体对象组件',
        '弹出框组件', '版本控制', '设计主题评分', '用户体验',
        '总分', '评级'
    ]
    
    if output_format == 'csv':
        filename = f'课程设计评分_{datetime.now().strftime("%Y%m%d")}.csv'
        with open(filename, 'w', newline='', encoding='utf-8-sig') as file:
            writer = csv.writer(file)
            writer.writerow(headers)
            for score in student_scores:
                writer.writerow(score)
    
    elif output_format == 'excel':
        filename = f'课程设计评分_{datetime.now().strftime("%Y%m%d")}.xlsx'
        df = pd.DataFrame(student_scores, columns=headers)
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 评分汇总表
            df.to_excel(writer, sheet_name='评分汇总', index=False)
            
            # 统计信息表
            stats = {
                '总人数': len(df),
                '平均分': df['总分'].mean(),
                '最高分': df['总分'].max(),
                '最低分': df['总分'].min(),
                '及格率': (df['总分'] >= 60).sum() / len(df) * 100
            }
            stats_df = pd.DataFrame(list(stats.items()), columns=['统计项', '数值'])
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
    
    return filename

# 使用示例
student_data = [
    ['张三', '计科1班', '001', '民宿预订系统', 4, 3, 2, 4, 3, 3, 4, 3, 4, 2, 3, 2, 5, 4, 5, 1, 2, 3, 5, 4, 75, '良好'],
    ['李四', '计科1班', '002', '在线商城', 3, 4, 1, 3, 2, 2, 3, 4, 3, 3, 4, 3, 4, 3, 4, 2, 1, 2, 4, 3, 68, '及格']
]

# 生成CSV文件
csv_file = generate_score_file(student_data, 'csv')
print(f'CSV文件已生成: {csv_file}')

# 生成Excel文件
excel_file = generate_score_file(student_data, 'excel')
print(f'Excel文件已生成: {excel_file}')
```

## AI批改结果转换

### 转换流程
1. **解析AI批改报告**: 提取各项评分数据
2. **数据验证**: 检查评分范围和格式
3. **格式转换**: 转换为标准评分文件格式
4. **文件生成**: 生成CSV或Excel文件

### 转换规则
```python
def parse_ai_report(report_text):
    """
    解析AI批改报告，提取评分数据
    """
    scores = {}
    
    # 正则表达式匹配评分
    import re
    
    # 匹配各项评分
    patterns = {
        '代码规范': r'1\. 代码规范: (\d+)/5分',
        '模块化': r'2\. 模块化: (\d+)/5分',
        '错误处理': r'3\. 错误处理: (\d+)/5分',
        # ... 其他项目
        '总分': r'总体评分: (\d+)/100分'
    }
    
    for key, pattern in patterns.items():
        match = re.search(pattern, report_text)
        if match:
            scores[key] = int(match.group(1))
    
    return scores

def determine_grade(total_score):
    """
    根据总分确定评级
    """
    if total_score >= 90:
        return '优秀'
    elif total_score >= 80:
        return '良好'
    elif total_score >= 70:
        return '中等'
    elif total_score >= 60:
        return '及格'
    else:
        return '不及格'
```

## 使用说明

### 步骤1: AI批改
使用AI自动化批改提示词对学生作品进行评分

### 步骤2: 数据提取
从AI批改报告中提取20个评分项的具体分数

### 步骤3: 文件生成
运行评分文件生成工具，生成标准格式的评分文件

### 步骤4: 质量检查
检查生成的评分文件，确保数据准确性

### 步骤5: 导入系统
将评分文件导入教务管理系统或成绩管理系统

## 注意事项

1. **数据准确性**: 确保AI评分结果准确无误
2. **格式一致性**: 保持评分文件格式的一致性
3. **备份保存**: 及时备份评分文件和原始数据
4. **权限管理**: 注意评分文件的访问权限控制
5. **版本控制**: 对评分文件进行版本管理

## 扩展功能

### 批量处理
- 支持批量处理多个学生作品
- 自动生成班级评分汇总
- 生成评分统计报告

### 可视化报告
- 生成评分分布图表
- 各项评分对比分析
- 班级整体表现分析

### 质量监控
- 评分一致性检查
- 异常评分预警
- 评分标准偏差分析

此工具确保了从AI批改到最终成绩录入的完整流程，提高了评分工作的效率和准确性。
